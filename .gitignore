# Logs
logs
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*
pnpm-debug.log*
lerna-debug.log*

node_modules
dist
dist-ssr
*.local

# Editor directories and files
.vscode/*
!.vscode/extensions.json
.idea
.DS_Store
*.suo
*.ntvs*
*.njsproj
*.sln
*.sw?
# 环境变量文件 - 包含敏感信息，不应提交到版本控制
.env
.env.local
.env.development.local
.env.test.local
.env.production.local

# 敏感配置文件
config/secrets.js
config/secrets.ts
*.secret.*
*.private.*

# 文档文件 - 除了README之外的md文件不提交到版本控制
*.md
*.MD
# 但是保留README文件
!README.md
!README.MD
!readme.md
!readme.MD

# SQL 脚本文件 - 不提交到版本控制
*.sql
*.SQL
scripts/*.sql
scripts/*.SQL
