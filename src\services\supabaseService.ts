import { supabase, TABLES, Photo, Album, Tag } from '../config/supabase'

// 检查Supabase配置的辅助函数
function checkSupabaseConfig() {
  if (!supabase) {
    throw new Error('Supabase配置无效，请检查环境变量设置')
  }
  return supabase
}

// 照片相关操作
export class PhotoService {
  // 获取所有照片（优化版本 - 只选择必要字段）
  static async getAllPhotos(): Promise<Photo[]> {
    const client = checkSupabaseConfig()

    const { data, error } = await client
      .from(TABLES.PHOTOS)
      .select('id, filename, title, description, date_taken, location, album_id')
      .order('date_taken', { ascending: false })
      .limit(100) // 限制返回数量，避免一次性加载过多数据

    if (error) {
      console.error('获取照片失败:', error)
      throw error
    }

    return data || []
  }

  // 根据相册ID获取照片（优化版本）
  static async getPhotosByAlbum(albumId: string): Promise<Photo[]> {
    const { data, error } = await supabase
      .from(TABLES.PHOTOS)
      .select('id, filename, title, description, date_taken, location, album_id')
      .eq('album_id', albumId)
      .order('date_taken', { ascending: false })
      .limit(50) // 限制单个相册返回数量

    if (error) {
      console.error('获取相册照片失败:', error)
      throw error
    }

    return data || []
  }

  // 分页获取照片
  static async getPhotosWithPagination(
    page: number = 0,
    limit: number = 20,
    albumId?: string
  ): Promise<{ photos: Photo[]; hasMore: boolean }> {
    let query = supabase
      .from(TABLES.PHOTOS)
      .select('id, filename, title, description, date_taken, location, album_id')
      .order('date_taken', { ascending: false })
      .range(page * limit, (page + 1) * limit - 1)

    if (albumId) {
      query = query.eq('album_id', albumId)
    }

    const { data, error } = await query

    if (error) {
      console.error('分页获取照片失败:', error)
      throw error
    }

    // 检查是否还有更多数据 - 改进的方法
    // 方法1：检查总数
    let totalCountQuery = supabase
      .from(TABLES.PHOTOS)
      .select('*', { count: 'exact', head: true })

    if (albumId) {
      totalCountQuery = totalCountQuery.eq('album_id', albumId)
    }

    const { count: totalCount } = await totalCountQuery
    const hasMore = totalCount ? (page + 1) * limit < totalCount : false

    return {
      photos: data || [],
      hasMore
    }
  }

  // 添加照片
  static async addPhoto(photo: Omit<Photo, 'id' | 'created_at' | 'updated_at'>): Promise<Photo> {
    const { data, error } = await supabase
      .from(TABLES.PHOTOS)
      .insert([photo])
      .select()
      .single()

    if (error) {
      console.error('添加照片失败:', error)
      throw error
    }

    return data
  }

  // 更新照片
  static async updatePhoto(id: string, updates: Partial<Photo>): Promise<Photo> {
    const { data, error } = await supabase
      .from(TABLES.PHOTOS)
      .update(updates)
      .eq('id', id)
      .select()
      .single()

    if (error) {
      console.error('更新照片失败:', error)
      throw error
    }

    return data
  }

  // 删除照片
  static async deletePhoto(id: string): Promise<void> {
    const { error } = await supabase
      .from(TABLES.PHOTOS)
      .delete()
      .eq('id', id)

    if (error) {
      console.error('删除照片失败:', error)
      throw error
    }
  }

  // 搜索照片（优化版本）
  static async searchPhotos(query: string): Promise<Photo[]> {
    const { data, error } = await supabase
      .from(TABLES.PHOTOS)
      .select('id, filename, title, description, date_taken, location, album_id')
      .or(`title.ilike.%${query}%,description.ilike.%${query}%,location.ilike.%${query}%`)
      .order('date_taken', { ascending: false })
      .limit(30) // 限制搜索结果数量

    if (error) {
      console.error('搜索照片失败:', error)
      throw error
    }

    return data || []
  }

  // 分页搜索照片
  static async searchPhotosWithPagination(
    query: string,
    page: number = 0,
    limit: number = 20
  ): Promise<{ photos: Photo[]; hasMore: boolean }> {
    const { data, error } = await supabase
      .from(TABLES.PHOTOS)
      .select('id, filename, title, description, date_taken, location, album_id')
      .or(`title.ilike.%${query}%,description.ilike.%${query}%,location.ilike.%${query}%`)
      .order('date_taken', { ascending: false })
      .range(page * limit, (page + 1) * limit - 1)

    if (error) {
      console.error('搜索照片失败:', error)
      throw error
    }

    // 检查是否还有更多数据
    const nextPageQuery = supabase
      .from(TABLES.PHOTOS)
      .select('id', { count: 'exact', head: true })
      .or(`title.ilike.%${query}%,description.ilike.%${query}%,location.ilike.%${query}%`)
      .range((page + 1) * limit, (page + 1) * limit)

    const { count } = await nextPageQuery
    const hasMore = (count || 0) > 0

    return {
      photos: data || [],
      hasMore
    }
  }
}

// 相册相关操作
export class AlbumService {
  // 获取所有相册
  static async getAllAlbums(): Promise<Album[]> {
    const { data, error } = await supabase
      .from(TABLES.ALBUMS)
      .select('*')
      .order('created_at', { ascending: false })

    if (error) {
      console.error('获取相册失败:', error)
      throw error
    }

    return data || []
  }

  // 根据ID获取相册
  static async getAlbumById(id: string): Promise<Album | null> {
    const { data, error } = await supabase
      .from(TABLES.ALBUMS)
      .select('*')
      .eq('id', id)
      .single()

    if (error) {
      console.error('获取相册失败:', error)
      return null
    }

    return data
  }

  // 添加相册
  static async addAlbum(album: Omit<Album, 'id' | 'created_at' | 'updated_at'>): Promise<Album> {
    const { data, error } = await supabase
      .from(TABLES.ALBUMS)
      .insert([album])
      .select()
      .single()

    if (error) {
      console.error('添加相册失败:', error)
      throw error
    }

    return data
  }

  // 更新相册
  static async updateAlbum(id: string, updates: Partial<Album>): Promise<Album> {
    const { data, error } = await supabase
      .from(TABLES.ALBUMS)
      .update(updates)
      .eq('id', id)
      .select()
      .single()

    if (error) {
      console.error('更新相册失败:', error)
      throw error
    }

    return data
  }

  // 删除相册
  static async deleteAlbum(id: string): Promise<void> {
    const { error } = await supabase
      .from(TABLES.ALBUMS)
      .delete()
      .eq('id', id)

    if (error) {
      console.error('删除相册失败:', error)
      throw error
    }
  }
}

// 标签相关操作
export class TagService {
  // 获取所有标签
  static async getAllTags(): Promise<Tag[]> {
    const { data, error } = await supabase
      .from(TABLES.TAGS)
      .select('*')
      .order('name')

    if (error) {
      console.error('获取标签失败:', error)
      throw error
    }

    return data || []
  }

  // 添加标签
  static async addTag(tag: Omit<Tag, 'id' | 'created_at' | 'updated_at'>): Promise<Tag> {
    const { data, error } = await supabase
      .from(TABLES.TAGS)
      .insert([tag])
      .select()
      .single()

    if (error) {
      console.error('添加标签失败:', error)
      throw error
    }

    return data
  }

  // 为照片添加标签
  static async addTagToPhoto(photoId: string, tagId: string): Promise<void> {
    const { error } = await supabase
      .from(TABLES.PHOTO_TAGS)
      .insert([{ photo_id: photoId, tag_id: tagId }])

    if (error) {
      console.error('为照片添加标签失败:', error)
      throw error
    }
  }

  // 从照片移除标签
  static async removeTagFromPhoto(photoId: string, tagId: string): Promise<void> {
    const { error } = await supabase
      .from(TABLES.PHOTO_TAGS)
      .delete()
      .eq('photo_id', photoId)
      .eq('tag_id', tagId)

    if (error) {
      console.error('从照片移除标签失败:', error)
      throw error
    }
  }

  // 获取照片的标签
  static async getPhotoTags(photoId: string): Promise<Tag[]> {
    const { data, error } = await supabase
      .from(TABLES.PHOTO_TAGS)
      .select(`
        ${TABLES.TAGS} (*)
      `)
      .eq('photo_id', photoId)

    if (error) {
      console.error('获取照片标签失败:', error)
      throw error
    }

    return data?.map(item => item.tags).filter(Boolean) || []
  }
}
