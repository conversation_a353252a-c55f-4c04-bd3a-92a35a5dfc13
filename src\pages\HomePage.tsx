import React, { useState, useMemo } from 'react';
import { CampusWaterfallGrid } from '../components/CampusWaterfallGrid';
import { CampusPhotoModal } from '../components/CampusPhotoModal';
import { CampusSearchFilter } from '../components/CampusSearchFilter';
import { CampusHeader } from '../components/CampusHeader';
import { CampusStats } from '../components/CampusStats';
import { RecentReunion } from '../components/RecentReunion';

import { ErrorBoundary } from '../components/ErrorBoundary';
import { FontProvider } from '../components/FontProvider';

import { SEOHead, SEOConfigs, generatePhotoSEO } from '../components/SEOHead';
import { StructuredData, StructuredDataConfigs } from '../components/StructuredData';
import { usePrivacyProtection } from '../hooks/usePrivacyProtection';

import { useNetworkStatus, useServiceWorker } from '../hooks/useNetworkStatus';
import { useSettings } from '../hooks/useSettings';
import { photos, Photo } from '../data/photos';
import { getAllYearCalculations, logYearCalculations } from '../utils/yearCalculations';
import { useSupabasePhotos, convertSupabasePhotosToLocal } from '../hooks/useSupabasePhotos';
import { shuffleArray, generateTimeSeed } from '../utils/arrayUtils';

function HomePage() {
  usePrivacyProtection();

  // Network status and service worker
  const networkStatus = useNetworkStatus();
  const { isRegistered } = useServiceWorker();

  // Settings
  const { settings } = useSettings();

  // 年份计算
  const { yearsKnown, yearsGraduated, currentYear } = getAllYearCalculations();

  // 页面状态 - 默认显示近期聚会页面
  const [currentPage, setCurrentPage] = useState<'recent' | 'campus'>('recent');
  const [selectedPhoto, setSelectedPhoto] = useState<Photo | null>(null);
  const [selectedPhotoIndex, setSelectedPhotoIndex] = useState(0);
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [searchTerm, setSearchTerm] = useState('');

  // 随机排序状态
  const [randomSeed, setRandomSeed] = useState(() => generateTimeSeed());
  const [enableRandomOrder, setEnableRandomOrder] = useState(true);

  // 启用Supabase数据源从数据库读取相册数据
  const useSupabaseData = true;

  // Supabase数据获取 - 根据搜索词动态获取
  const {
    photos: supabasePhotos,
    loading: supabaseLoading,
    error: supabaseError,
    hasMore: supabaseHasMore,
    loadMore: supabaseLoadMore,
    refresh: supabaseRefresh
  } = useSupabasePhotos(undefined, searchTerm);



  // 转换Supabase数据为本地格式
  const convertedSupabasePhotos = useMemo(() => {
    const converted = convertSupabasePhotosToLocal(supabasePhotos);
    return converted;
  }, [supabasePhotos]);

  // 根据数据源选择照片数据和状态
  const activePhotos = useSupabaseData ? convertedSupabasePhotos : photos;
  const isLoading = useSupabaseData ? supabaseLoading : false;
  const hasError = useSupabaseData ? supabaseError : null;
  const activeHasMore = useSupabaseData ? supabaseHasMore : hasMore;
  const activeLoadMore = useSupabaseData ? supabaseLoadMore : loadMore;

  // 搜索过滤和随机排序
  const filteredPhotos = useMemo(() => {
    let filtered = activePhotos;

    // 搜索过滤
    if (searchTerm.trim()) {
      const searchLower = searchTerm.toLowerCase();
      filtered = activePhotos.filter(photo =>
        photo.title?.toLowerCase().includes(searchLower) ||
        photo.description?.toLowerCase().includes(searchLower) ||
        photo.location?.toLowerCase().includes(searchLower) ||
        photo.tags?.some(tag => tag.toLowerCase().includes(searchLower))
      );
    }

    // 随机排序
    if (enableRandomOrder) {
      // 使用随机种子确保每次刷新都有不同的顺序
      const shuffled = shuffleArray(filtered);
      return shuffled;
    }

    return filtered;
  }, [activePhotos, searchTerm, enableRandomOrder, randomSeed]);

  // 分页状态
  const [displayedCount, setDisplayedCount] = useState(20);

  // 最终显示的照片数据
  const finalPhotos = useSupabaseData ? filteredPhotos : filteredPhotos.slice(0, displayedCount);
  const finalHasMore = useSupabaseData ? activeHasMore : displayedCount < filteredPhotos.length;

  const finalLoadMore = useSupabaseData ? activeLoadMore : () => {
    setDisplayedCount(prev => prev + 10);
  };

  // 随机刷新函数
  const refreshRandomOrder = () => {
    if (enableRandomOrder) {
      // 生成新的随机种子
      setRandomSeed(generateTimeSeed());
      // 重置显示数量到初始值
      setDisplayedCount(20);
    }
  };

  // 照片模态框处理
  const handlePhotoClick = (photo: Photo, index: number) => {
    setSelectedPhoto(photo);
    setSelectedPhotoIndex(index);
    setIsModalOpen(true);
  };

  const handleCloseModal = () => {
    setIsModalOpen(false);
    setSelectedPhoto(null);
  };

  const handlePrevPhoto = () => {
    if (selectedPhotoIndex > 0) {
      const newIndex = selectedPhotoIndex - 1;
      setSelectedPhotoIndex(newIndex);
      setSelectedPhoto(displayedPhotos[newIndex]);
    }
  };

  const handleNextPhoto = () => {
    if (selectedPhotoIndex < displayedPhotos.length - 1) {
      const newIndex = selectedPhotoIndex + 1;
      setSelectedPhotoIndex(newIndex);
      setSelectedPhoto(displayedPhotos[newIndex]);
    }
  };

  // 记录年份计算结果
  logYearCalculations();

  console.log('HomePage rendering, currentPage:', currentPage);
  console.log('displayedPhotos length:', displayedPhotos.length);
  console.log('isLoading:', isLoading);
  console.log('hasError:', hasError);

  return (
    <FontProvider>
      <div className="min-h-screen bg-gradient-to-br from-amber-50 via-orange-50 to-red-50">

          {/* SEO Head */}
          <SEOHead
            config={currentPage === 'recent' ? SEOConfigs.recentReunion : SEOConfigs.campusMemories}
            photo={selectedPhoto ? generatePhotoSEO(selectedPhoto) : undefined}
          />

          {/* Structured Data */}
          <StructuredData
            config={currentPage === 'recent' ? StructuredDataConfigs.recentReunion : StructuredDataConfigs.campusMemories}
            photos={displayedPhotos}
          />

          {/* Recent Reunion Page */}
          {currentPage === 'recent' && (
            <RecentReunion onEnterAlbum={() => setCurrentPage('campus')} />
          )}

          {/* Campus Memories Page */}
          {currentPage === 'campus' && (
            <>
              {/* Header - 只在相册页面显示 */}
              <CampusHeader
                yearsKnown={yearsKnown}
                yearsGraduated={yearsGraduated}
                currentYear={currentYear}
              />
              {/* Navigation Button */}
              <div className="fixed top-4 right-4 z-50">
                <button
                  onClick={() => setCurrentPage('recent')}
                  className="bg-gradient-to-r from-amber-500 to-orange-500 hover:from-amber-600 hover:to-orange-600 text-white font-bold py-2 px-4 rounded-full shadow-lg transition-all duration-200 hover:scale-105"
                >
                  近期聚会
                </button>
              </div>

              {/* Stats */}
              <CampusStats
                yearsKnown={yearsKnown}
                yearsGraduated={yearsGraduated}
                totalPhotos={activePhotos.length}
              />

              {/* Content section */}
              <div className="container mx-auto px-4 py-6 md:py-8">

                {/* Search Controls */}
                <CampusSearchFilter
                  searchTerm={searchTerm}
                  onSearchChange={setSearchTerm}
                  selectedYear=""
                  onYearChange={() => {}}
                  availableYears={[]}
                  onRandomShuffle={refreshRandomOrder}
                />

                {/* Error State */}
                {hasError && (
                  <div className="text-center py-12">
                    <div className="bg-red-50 border border-red-200 rounded-lg p-6 max-w-md mx-auto">
                      <p className="text-red-600 mb-4">数据加载失败</p>
                      <button
                        onClick={() => useSupabaseData ? supabaseRefresh() : window.location.reload()}
                        className="bg-red-500 hover:bg-red-600 text-white px-4 py-2 rounded-lg transition-colors"
                      >
                        重试
                      </button>
                    </div>
                  </div>
                )}

                {/* Loading State */}
                {isLoading && finalPhotos.length === 0 && (
                  <div className="text-center py-12">
                    <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-amber-600 mx-auto mb-4"></div>
                    <p className="text-amber-600">正在加载照片...</p>
                  </div>
                )}

                {/* No Results */}
                {!isLoading && !hasError && finalPhotos.length === 0 && (
                  <div className="text-center py-12">
                    <p className="text-gray-500 text-lg">
                      {searchTerm ? `没有找到包含"${searchTerm}"的照片` : '暂无照片'}
                    </p>
                  </div>
                )}

                {/* Photo Grid */}
                {finalPhotos.length > 0 && (
                  <div className="space-y-8">
                    <CampusWaterfallGrid
                      photos={finalPhotos}
                      onPhotoClick={handlePhotoClick}
                      isLoading={isLoading}
                      hasMore={finalHasMore}
                    />

                    {/* Load More Button - 只在还有更多内容时显示 */}
                    {finalHasMore && (
                      <div className="text-center py-8">
                        <button
                          onClick={finalLoadMore}
                          disabled={isLoading}
                          className="bg-gradient-to-r from-amber-500 to-orange-500 hover:from-amber-600 hover:to-orange-600 disabled:from-gray-400 disabled:to-gray-500 text-white font-bold py-3 px-8 rounded-full shadow-lg transform transition-all duration-200 hover:scale-105 disabled:scale-100 disabled:cursor-not-allowed"
                        >
                          {isLoading ? '加载中...' : '加载更多'}
                        </button>
                      </div>
                    )}
                  </div>
                )}
              </div>
            </>
          )}

          {/* Photo Modal */}
          <CampusPhotoModal
            isOpen={isModalOpen}
            photo={selectedPhoto}
            onClose={handleCloseModal}
            onPrev={handlePrevPhoto}
            onNext={handleNextPhoto}
            hasPrev={selectedPhotoIndex > 0}
            hasNext={selectedPhotoIndex < displayedPhotos.length - 1}
            currentIndex={selectedPhotoIndex}
            totalPhotos={displayedPhotos.length}
          />
        </div>
    </FontProvider>
  );
}

export default HomePage;
