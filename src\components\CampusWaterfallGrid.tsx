import React, { useState, useEffect, useRef, useMemo, useCallback } from 'react';
import { ImageProtection } from './ImageProtection';
import { LazyImage } from './LazyImage';
import { LoadingSpinner } from './LoadingSpinner';

interface Photo {
  id: number;
  src: string;
  title: string;
  year: string;
  description: string;
}

interface CampusWaterfallGridProps {
  photos: Photo[];
  onPhotoClick: (photo: Photo, index: number) => void;
  isLoading?: boolean;
  hasMore?: boolean;
}

export const CampusWaterfallGrid: React.FC<CampusWaterfallGridProps> = ({
  photos,
  onPhotoClick,
  isLoading = false,
  hasMore = false
}) => {

  const [columns, setColumns] = useState(3);
  const [imageHeights, setImageHeights] = useState<{ [key: number]: number }>({});
  const [columnPhotos, setColumnPhotos] = useState<Photo[][]>([]);
  const [lastPhotoCount, setLastPhotoCount] = useState(0);
  const gridRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    const updateColumns = () => {
      const width = window.innerWidth;
      // 更精细的响应式断点
      if (width < 480) setColumns(1);        // 小手机
      else if (width < 640) setColumns(1);   // 大手机
      else if (width < 768) setColumns(2);   // 小平板
      else if (width < 1024) setColumns(2);  // 平板
      else if (width < 1280) setColumns(3);  // 小桌面
      else if (width < 1536) setColumns(3);  // 桌面
      else setColumns(4);                    // 大桌面
    };

    updateColumns();

    // 使用防抖来优化性能
    let timeoutId: NodeJS.Timeout;
    const debouncedUpdateColumns = () => {
      clearTimeout(timeoutId);
      timeoutId = setTimeout(updateColumns, 150);
    };

    window.addEventListener('resize', debouncedUpdateColumns);
    return () => {
      window.removeEventListener('resize', debouncedUpdateColumns);
      clearTimeout(timeoutId);
    };
  }, []);

  // 初始化列数组
  useEffect(() => {
    setColumnPhotos(Array.from({ length: columns }, () => []));
  }, [columns]);

  // 增量式管理列照片 - 只处理新增的照片
  useEffect(() => {
    if (photos.length === 0) {
      setColumnPhotos(Array.from({ length: columns }, () => []));
      setLastPhotoCount(0);
      return;
    }

    // 如果照片数量减少了（比如搜索或刷新），重新分配所有照片
    if (photos.length < lastPhotoCount) {
      const newCols: Photo[][] = Array.from({ length: columns }, () => []);
      photos.forEach((photo, index) => {
        const columnIndex = index % columns;
        newCols[columnIndex].push(photo);
      });
      setColumnPhotos(newCols);
      setLastPhotoCount(photos.length);
      return;
    }

    // 只处理新增的照片
    if (photos.length > lastPhotoCount) {
      const newPhotos = photos.slice(lastPhotoCount);
      setColumnPhotos(prevCols => {
        const newCols = prevCols.map(col => [...col]); // 复制现有列
        newPhotos.forEach((photo, index) => {
          const globalIndex = lastPhotoCount + index;
          const columnIndex = globalIndex % columns;
          newCols[columnIndex].push(photo);
        });
        return newCols;
      });
      setLastPhotoCount(photos.length);
    }
  }, [photos, columns, lastPhotoCount]);

  // Preload images to get their dimensions
  useEffect(() => {
    photos.forEach(photo => {
      if (!imageHeights[photo.id]) {
        const img = new Image();
        img.onload = () => {
          setImageHeights(prev => ({
            ...prev,
            [photo.id]: img.height / img.width
          }));
        };
        img.src = photo.src;
      }
    });
  }, [photos, imageHeights]);



  return (
    <div className="relative">
      <div 
        ref={gridRef}
        className={`grid gap-4 md:gap-6 ${
          columns === 1 ? 'grid-cols-1' :
          columns === 2 ? 'grid-cols-2' :
          columns === 3 ? 'grid-cols-3' :
          'grid-cols-4'
        }`}
      >
        {columnPhotos.map((columnPhotoList, columnIndex) => (
          <div key={columnIndex} className="flex flex-col gap-4 md:gap-6">
            {columnPhotoList.map((photo, photoIndex) => {
              const actualIndex = photos.findIndex(p => p.id === photo.id);
              return (
                <div
                  key={photo.id}
                  className="bg-white rounded-lg shadow-xl hover:shadow-2xl transition-all duration-500 transform hover:-translate-y-2 hover:scale-[1.02] overflow-hidden border-4 md:border-8 border-amber-100 relative group cursor-pointer campus-animate-fadeIn"
                  style={{
                    boxShadow: '0 8px 25px rgba(139, 69, 19, 0.2), 0 0 0 1px rgba(255, 255, 255, 0.8)',
                    background: 'linear-gradient(135deg, #ffffff 0%, #fefcf7 100%)',
                    animationDelay: `${photoIndex * 0.1}s`
                  }}
                >
                  {/* Vintage photo corners */}
                  <div className="absolute top-1 left-1 md:top-2 md:left-2 w-2 h-2 md:w-4 md:h-4 border-l-2 border-t-2 border-amber-400 z-20"></div>
                  <div className="absolute top-1 right-1 md:top-2 md:right-2 w-2 h-2 md:w-4 md:h-4 border-r-2 border-t-2 border-amber-400 z-20"></div>
                  <div className="absolute bottom-16 md:bottom-20 left-1 md:left-2 w-2 h-2 md:w-4 md:h-4 border-l-2 border-b-2 border-amber-400 z-20"></div>
                  <div className="absolute bottom-16 md:bottom-20 right-1 md:right-2 w-2 h-2 md:w-4 md:h-4 border-r-2 border-b-2 border-amber-400 z-20"></div>
                  
                  {/* Hover glow effect */}
                  <div 
                    className="absolute inset-0 opacity-0 group-hover:opacity-100 transition-opacity duration-500 pointer-events-none"
                    style={{
                      background: 'radial-gradient(circle at center, rgba(255, 193, 7, 0.1) 0%, transparent 70%)',
                      zIndex: 1
                    }}
                  />
                  
                  <ImageProtection
                    src={photo.src}
                    alt={photo.title}
                    className="w-full relative z-10"
                    onClick={() => onPhotoClick(photo, actualIndex)}
                  />
                  
                  <div className="p-3 md:p-6 relative z-10 bg-gradient-to-b from-amber-50 to-orange-50">
                    <h3 
                      className="text-lg md:text-xl font-bold text-amber-800 mb-1 md:mb-2 campus-line-clamp-2"
                      style={{
                        fontFamily: '"Ma Shan Zheng", cursive',
                        textShadow: '1px 1px 2px rgba(139, 69, 19, 0.2)'
                      }}
                    >
                      {photo.title}
                    </h3>
                    
                    <p 
                      className="text-orange-600 text-xs md:text-sm font-bold mb-2 md:mb-3"
                      style={{
                        fontFamily: '"Zhi Mang Xing", cursive',
                        transform: 'rotate(-0.5deg)'
                      }}
                    >
                      {photo.year}
                    </p>
                    
                    <p 
                      className="text-amber-700 text-xs md:text-sm leading-relaxed campus-line-clamp-3"
                      style={{ fontFamily: '"Long Cang", serif' }}
                    >
                      {photo.description}
                    </p>
                    
                    {/* Decorative line */}
                    <div 
                      className="absolute bottom-0 left-3 right-3 md:left-6 md:right-6 h-0.5 md:h-1 bg-gradient-to-r from-transparent via-amber-300 to-transparent opacity-60"
                    />
                  </div>
                </div>
              );
            })}
          </div>
        ))}
      </div>

      {/* Loading indicator */}
      {isLoading && (
        <div className="mt-8">
          <LoadingSpinner 
            size="lg" 
            text="正在加载更多珍贵回忆..."
          />
        </div>
      )}

      {/* No more content indicator */}
      {!hasMore && photos.length > 0 && (
        <div className="mt-12 text-center">
          <div 
            className="inline-block bg-gradient-to-r from-amber-100 to-orange-100 px-8 py-4 rounded-full shadow-lg border-4 border-amber-200"
            style={{
              boxShadow: '0 8px 25px rgba(139, 69, 19, 0.15), inset 0 1px 0 rgba(255, 255, 255, 0.6)'
            }}
          >
            <div 
              className="text-amber-800 font-bold text-lg mb-1"
              style={{
                fontFamily: '"Ma Shan Zheng", cursive',
                textShadow: '2px 2px 4px rgba(139, 69, 19, 0.2)'
              }}
            >
              所有珍贵回忆已加载完毕
            </div>
            <div 
              className="text-orange-600 text-sm"
              style={{
                fontFamily: '"Zhi Mang Xing", cursive',
                transform: 'rotate(-0.5deg)'
              }}
            >
              "时光不老，我们不散"
            </div>
          </div>
        </div>
      )}

      <style>
        {`
          @keyframes campus-fadeIn {
            from {
              opacity: 0;
              transform: translateY(20px);
            }
            to {
              opacity: 1;
              transform: translateY(0);
            }
          }

          .campus-animate-fadeIn {
            animation: campus-fadeIn 0.6s ease-out forwards;
            opacity: 0;
          }

          .campus-line-clamp-2 {
            display: -webkit-box;
            -webkit-line-clamp: 2;
            -webkit-box-orient: vertical;
            overflow: hidden;
          }

          .campus-line-clamp-3 {
            display: -webkit-box;
            -webkit-line-clamp: 3;
            -webkit-box-orient: vertical;
            overflow: hidden;
          }
        `}
      </style>
    </div>
  );
};