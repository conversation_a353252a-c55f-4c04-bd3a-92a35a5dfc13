import React, { useState, useEffect, useMemo, memo } from 'react';
import { photos, Photo } from '../data/photos';

// 独立的照片卡片组件，使用memo避免不必要的重新渲染
const PhotoCard = memo(({
  photo,
  actualIndex,
  isNewlyLoaded,
  photoIndex,
  onPhotoClick
}: {
  photo: Photo;
  actualIndex: number;
  isNewlyLoaded: boolean;
  photoIndex: number;
  onPhotoClick: (photo: Photo, index: number) => void;
}) => {
  return (
    <div
      className={`bg-white rounded-lg shadow-lg overflow-hidden hover:shadow-xl transform hover:scale-[1.02] transition-all duration-300 cursor-pointer border-2 border-amber-100 ${isNewlyLoaded ? 'animate-fadeIn' : ''}`}
      onClick={() => onPhotoClick(photo, actualIndex)}
      style={{
        boxShadow: '0 4px 20px rgba(245, 158, 11, 0.15)',
        animationDelay: isNewlyLoaded ? `${photoIndex * 0.1}s` : '0s'
      }}
    >
      <div className="overflow-hidden">
        <img
          src={photo.src}
          alt={photo.title}
          className="w-full h-auto object-cover hover:scale-110 transition-transform duration-500"
          loading="lazy"
          onError={(e) => {
            e.currentTarget.src = '/images/placeholder.svg';
          }}
        />
      </div>
      <div className="p-4">
        <h3 className="font-bold text-lg text-amber-800 mb-2 line-clamp-2">
          {photo.title}
        </h3>
        <p className="text-sm text-amber-600 mb-2">{photo.year}</p>
        <p className="text-sm text-gray-700 line-clamp-3">
          {photo.description}
        </p>
      </div>
    </div>
  );
});

// 简化的相册主页组件
function SimpleHomePage() {
  console.log('SimpleHomePage 组件正在渲染');
  console.log('SimpleHomePage - 当前路径:', window.location.pathname);

  const [currentPage, setCurrentPage] = useState<'recent' | 'campus'>('recent');

  // 添加CSS动画样式
  useEffect(() => {
    const style = document.createElement('style');
    style.textContent = `
      @keyframes fadeIn {
        from {
          opacity: 0;
          transform: translateY(20px);
        }
        to {
          opacity: 1;
          transform: translateY(0);
        }
      }
      .animate-fadeIn {
        animation: fadeIn 0.6s ease-out forwards;
        opacity: 0;
      }
    `;
    document.head.appendChild(style);

    return () => {
      document.head.removeChild(style);
    };
  }, []);

  // 简化的聚会页面
  const RecentReunionPage = () => (
    <div className="min-h-screen bg-gradient-to-br from-amber-50 via-orange-50 to-red-50">
      {/* 标题区域 */}
      <div className="relative min-h-screen flex items-center justify-center">
        {/* 背景装饰 */}
        <div className="absolute inset-0 overflow-hidden">
          <div className="absolute top-20 left-20 w-32 h-32 bg-amber-200 rounded-full opacity-20 animate-pulse"></div>
          <div className="absolute bottom-20 right-20 w-24 h-24 bg-orange-200 rounded-full opacity-20 animate-pulse delay-1000"></div>
          <div className="absolute top-1/2 left-10 w-16 h-16 bg-red-200 rounded-full opacity-20 animate-pulse delay-2000"></div>
        </div>

        {/* 主要内容 */}
        <div className="relative z-10 text-center px-4">
          <h1 className="text-6xl md:text-8xl font-bold text-amber-600 mb-6 animate-fade-in">
            212班相册
          </h1>
          <p className="text-xl md:text-2xl text-amber-700 mb-8 animate-fade-in-delay">
            青春的回忆，永远的友谊
          </p>
          
          {/* 毕业年份信息 */}
          <div className="bg-white/80 backdrop-blur-sm rounded-lg p-6 mb-8 shadow-lg">
            <p className="text-lg text-amber-800">
              🎓 2006年毕业 · 相识 {new Date().getFullYear() - 2003} 年 · 毕业 {new Date().getFullYear() - 2006} 年
            </p>
          </div>

          {/* 操作按钮 */}
          <div className="space-y-4">
            <button
              onClick={() => setCurrentPage('campus')}
              className="bg-gradient-to-r from-amber-500 to-orange-500 hover:from-amber-600 hover:to-orange-600 text-white font-bold py-4 px-8 rounded-full shadow-lg transform transition-all duration-200 hover:scale-105 text-lg"
            >
              📸 进入相册
            </button>
            
            <div className="flex gap-4 justify-center mt-6">
              <a 
                href="/AdminPanel"
                className="bg-gradient-to-r from-green-500 to-emerald-500 hover:from-green-600 hover:to-emerald-600 text-white font-bold py-3 px-6 rounded-full shadow-lg transform transition-all duration-200 hover:scale-105"
              >
                ⚙️ 管理面板
              </a>
              <a 
                href="/debug-env"
                className="bg-gradient-to-r from-blue-500 to-cyan-500 hover:from-blue-600 hover:to-cyan-600 text-white font-bold py-3 px-6 rounded-full shadow-lg transform transition-all duration-200 hover:scale-105"
              >
                🔧 环境检查
              </a>
            </div>
          </div>
        </div>

        {/* 右上角倒计时 */}
        <div className="absolute top-8 right-8 z-30">
          <div className="bg-amber-500/90 backdrop-blur-md rounded-lg px-4 py-3 border border-amber-400 shadow-lg">
            <div className="text-center text-white">
              <div className="text-sm font-semibold mb-1">毕业20周年倒计时</div>
              <div className="text-2xl font-bold">{Math.max(1, Math.ceil((2026 - new Date().getFullYear())))}年</div>
              <div className="text-xs opacity-90">(距离2026年)</div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );

  // 简化的相册页面
  const CampusAlbumPage = () => {
    const [selectedPhoto, setSelectedPhoto] = useState<Photo | null>(null);
    const [currentIndex, setCurrentIndex] = useState(0);
    const [displayedPhotos, setDisplayedPhotos] = useState<Photo[]>([]);
    const [loadedCount, setLoadedCount] = useState(0);
    const [isLoading, setIsLoading] = useState(false);
    const [columns, setColumns] = useState(3);
    const [columnPhotos, setColumnPhotos] = useState<Photo[][]>([]);
    const [newlyLoadedIds, setNewlyLoadedIds] = useState<Set<number>>(new Set());

    const INITIAL_LOAD = 12;
    const LOAD_MORE_COUNT = 8;

    // 响应式列数
    useEffect(() => {
      const updateColumns = () => {
        if (window.innerWidth < 640) setColumns(1);
        else if (window.innerWidth < 1024) setColumns(2);
        else if (window.innerWidth < 1536) setColumns(3);
        else setColumns(4);
      };

      updateColumns();
      window.addEventListener('resize', updateColumns);
      return () => window.removeEventListener('resize', updateColumns);
    }, []);

    // 初始化列数组
    useEffect(() => {
      setColumnPhotos(Array.from({ length: columns }, () => []));
    }, [columns]);

    // 初始加载
    useEffect(() => {
      const initialPhotos = photos.slice(0, INITIAL_LOAD);
      setDisplayedPhotos(initialPhotos);
      setLoadedCount(INITIAL_LOAD);

      // 初始化列分配
      const cols: Photo[][] = Array.from({ length: columns }, () => []);
      initialPhotos.forEach((photo, index) => {
        const columnIndex = index % columns;
        cols[columnIndex].push(photo);
      });
      setColumnPhotos(cols);
    }, []);

    const hasMore = loadedCount < photos.length;

    // 加载更多照片
    const loadMore = () => {
      if (isLoading || !hasMore) return;

      setIsLoading(true);

      // 模拟加载延迟
      setTimeout(() => {
        const newCount = Math.min(loadedCount + LOAD_MORE_COUNT, photos.length);
        const newPhotos = photos.slice(loadedCount, newCount); // 只获取新的照片
        console.log(`加载更多: 从第${loadedCount}张到第${newCount}张，新增${newPhotos.length}张照片`);

        // 追加到displayedPhotos
        setDisplayedPhotos(prev => {
          console.log(`追加前: ${prev.length}张照片，追加后: ${prev.length + newPhotos.length}张照片`);
          return [...prev, ...newPhotos];
        });

        // 增量式追加到列中
        setColumnPhotos(prevCols => {
          const newCols = prevCols.map(col => [...col]); // 复制现有列
          newPhotos.forEach((photo, index) => {
            const globalIndex = loadedCount + index;
            const columnIndex = globalIndex % columns;
            newCols[columnIndex].push(photo);
          });
          return newCols;
        });

        // 标记新加载的照片ID
        const newIds = new Set(newPhotos.map(p => p.id));
        setNewlyLoadedIds(newIds);

        // 3秒后清除新照片标记
        setTimeout(() => {
          setNewlyLoadedIds(new Set());
        }, 3000);

        setLoadedCount(newCount);
        setIsLoading(false);
      }, 500);
    };

    const openPhotoModal = (photo: Photo, index: number) => {
      setSelectedPhoto(photo);
      setCurrentIndex(index);
    };

    const closePhotoModal = () => {
      setSelectedPhoto(null);
    };

    const nextPhoto = () => {
      const nextIndex = (currentIndex + 1) % displayedPhotos.length;
      setCurrentIndex(nextIndex);
      setSelectedPhoto(displayedPhotos[nextIndex]);
    };

    const prevPhoto = () => {
      const prevIndex = (currentIndex - 1 + displayedPhotos.length) % displayedPhotos.length;
      setCurrentIndex(prevIndex);
      setSelectedPhoto(displayedPhotos[prevIndex]);
    };

    // 当列数改变时重新分配所有照片
    useEffect(() => {
      if (displayedPhotos.length > 0) {
        const cols: Photo[][] = Array.from({ length: columns }, () => []);
        displayedPhotos.forEach((photo, index) => {
          const columnIndex = index % columns;
          cols[columnIndex].push(photo);
        });
        setColumnPhotos(cols);
      }
    }, [columns]); // 只在列数改变时重新分配

    return (
      <div className="min-h-screen bg-gradient-to-br from-amber-50 via-orange-50 to-red-50">
        <div className="container mx-auto px-4 py-8">
          {/* 头部导航 */}
          <div className="flex items-center justify-between mb-8">
            <button
              onClick={() => setCurrentPage('recent')}
              className="bg-gradient-to-r from-gray-500 to-gray-600 hover:from-gray-600 hover:to-gray-700 text-white font-bold py-2 px-4 rounded-full shadow-lg transform transition-all duration-200 hover:scale-105"
            >
              ← 返回首页
            </button>

            <h1 className="text-3xl md:text-4xl font-bold text-amber-600">
              📸 校园回忆
            </h1>

            <div className="w-20"></div> {/* 占位符保持居中 */}
          </div>

          {/* 瀑布流照片网格 */}
          <div className={`grid gap-4 md:gap-6 ${
            columns === 1 ? 'grid-cols-1' :
            columns === 2 ? 'grid-cols-2' :
            columns === 3 ? 'grid-cols-3' :
            'grid-cols-4'
          }`}>
            {columnPhotos.map((columnPhotoList, columnIndex) => (
              <div key={columnIndex} className="flex flex-col gap-4 md:gap-6">
                {columnPhotoList.map((photo, photoIndex) => {
                  const actualIndex = displayedPhotos.findIndex(p => p.id === photo.id);
                  const isNewlyLoaded = newlyLoadedIds.has(photo.id);
                  return (
                    <PhotoCard
                      key={`photo-${photo.id}`}
                      photo={photo}
                      actualIndex={actualIndex}
                      isNewlyLoaded={isNewlyLoaded}
                      photoIndex={photoIndex}
                      onPhotoClick={openPhotoModal}
                    />
                  );
                })}
              </div>
            ))}
          </div>

          {/* 加载更多按钮 */}
          {hasMore && (
            <div className="text-center mt-12">
              <button
                onClick={loadMore}
                disabled={isLoading}
                className="bg-gradient-to-r from-amber-500 to-orange-500 hover:from-amber-600 hover:to-orange-600 disabled:from-gray-400 disabled:to-gray-500 text-white font-bold py-3 px-8 rounded-full shadow-lg transform transition-all duration-200 hover:scale-105 disabled:scale-100 disabled:cursor-not-allowed"
              >
                {isLoading ? (
                  <span className="flex items-center">
                    <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                    加载中...
                  </span>
                ) : (
                  '加载更多'
                )}
              </button>
            </div>
          )}

          {/* 状态提示 */}
          <div className="text-center mt-8">
            <div className="bg-white/80 backdrop-blur-sm rounded-lg p-4 shadow-lg inline-block">
              <p className="text-sm text-amber-700">
                📸 已显示 {displayedPhotos.length} 张照片，共 {photos.length} 张
              </p>
              {!hasMore && (
                <p className="text-xs text-gray-600 mt-1">
                  所有照片已加载完成
                </p>
              )}
            </div>
          </div>
        </div>

        {/* 照片模态框 */}
        {selectedPhoto && (
          <div className="fixed inset-0 bg-black bg-opacity-90 flex items-center justify-center z-50 p-4">
            <div className="relative max-w-4xl max-h-full">
              {/* 关闭按钮 */}
              <button
                onClick={closePhotoModal}
                className="absolute top-4 right-4 z-10 bg-white/20 hover:bg-white/30 text-white rounded-full p-2 transition-colors"
              >
                ✕
              </button>

              {/* 上一张按钮 */}
              <button
                onClick={prevPhoto}
                className="absolute left-4 top-1/2 transform -translate-y-1/2 z-10 bg-white/20 hover:bg-white/30 text-white rounded-full p-2 transition-colors"
              >
                ←
              </button>

              {/* 下一张按钮 */}
              <button
                onClick={nextPhoto}
                className="absolute right-4 top-1/2 transform -translate-y-1/2 z-10 bg-white/20 hover:bg-white/30 text-white rounded-full p-2 transition-colors"
              >
                →
              </button>

              {/* 照片 */}
              <img
                src={selectedPhoto.src}
                alt={selectedPhoto.title}
                className="max-w-full max-h-[80vh] object-contain rounded-lg"
              />

              {/* 照片信息 */}
              <div className="absolute bottom-0 left-0 right-0 bg-gradient-to-t from-black/80 to-transparent text-white p-6 rounded-b-lg">
                <h3 className="text-xl font-bold mb-2">{selectedPhoto.title}</h3>
                <p className="text-sm opacity-90 mb-1">{selectedPhoto.year}</p>
                <p className="text-sm opacity-80">{selectedPhoto.description}</p>
                <p className="text-xs opacity-60 mt-2">
                  {currentIndex + 1} / {displayedPhotos.length}
                </p>
              </div>
            </div>
          </div>
        )}
      </div>
    );
  };

  return (
    <>
      {currentPage === 'recent' && <RecentReunionPage />}
      {currentPage === 'campus' && <CampusAlbumPage />}
    </>
  );
}

export default SimpleHomePage;
