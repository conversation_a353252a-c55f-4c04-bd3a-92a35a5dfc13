import { useState, useEffect, useCallback } from 'react'
import { PhotoService, AlbumService } from '../services/supabaseService'
import { Photo, Album } from '../config/supabase'
import { usePerformanceMonitor } from './usePerformanceMonitor'

// 照片加载Hook
export function useSupabasePhotos(albumId?: string, searchTerm?: string) {
  const [photos, setPhotos] = useState<Photo[]>([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const [hasMore, setHasMore] = useState(true)
  const [page, setPage] = useState(0)
  // 暂时禁用性能监控
  // const { measureSupabaseResponse, logError } = usePerformanceMonitor()

  // 加载照片
  const loadPhotos = useCallback(async (reset = false) => {
    try {
      setLoading(true)
      setError(null)

      const currentPage = reset ? 0 : page

      // 根据是否有搜索词选择不同的API
      const { photos: newPhotos, hasMore: moreAvailable } = searchTerm
        ? await PhotoService.searchPhotosWithPagination(searchTerm, currentPage, 10) // 每页10张照片
        : await PhotoService.getPhotosWithPagination(currentPage, 10, albumId, reset && currentPage === 0) // 初始加载时随机化

      if (reset) {
        setPhotos(newPhotos)
        setPage(1)
      } else {
        setPhotos(prev => [...prev, ...newPhotos])
        setPage(prev => prev + 1)
      }

      setHasMore(moreAvailable)
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : '加载照片失败'
      setError(errorMessage)
      // logError({
      //   type: 'supabase',
      //   message: `加载照片失败: ${errorMessage}`
      // })
    } finally {
      setLoading(false)
    }
  }, [page, albumId, searchTerm])

  // 初始加载和搜索词变化时重新加载
  useEffect(() => {
    setPage(0) // 重置页码
    loadPhotos(true)
  }, [albumId, searchTerm])

  // 加载更多
  const loadMore = useCallback(() => {
    if (!loading && hasMore) {
      loadPhotos(false)
    }
  }, [loading, hasMore, loadPhotos])

  // 刷新
  const refresh = useCallback(() => {
    setPage(0)
    loadPhotos(true)
  }, [loadPhotos])

  return {
    photos,
    loading,
    error,
    hasMore,
    loadMore,
    refresh
  }
}

// 相册列表Hook
export function useSupabaseAlbums() {
  const [albums, setAlbums] = useState<Album[]>([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  const loadAlbums = useCallback(async () => {
    try {
      setLoading(true)
      setError(null)
      const albumsData = await AlbumService.getAllAlbums()
      setAlbums(albumsData)
    } catch (err) {
      setError(err instanceof Error ? err.message : '加载相册失败')
      console.error('加载相册失败:', err)
    } finally {
      setLoading(false)
    }
  }, [])

  useEffect(() => {
    loadAlbums()
  }, [loadAlbums])

  return {
    albums,
    loading,
    error,
    refresh: loadAlbums
  }
}

// 照片搜索Hook
export function useSupabasePhotoSearch() {
  const [photos, setPhotos] = useState<Photo[]>([])
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)

  const search = useCallback(async (query: string) => {
    if (!query.trim()) {
      setPhotos([])
      return
    }

    try {
      setLoading(true)
      setError(null)
      const results = await PhotoService.searchPhotos(query)
      setPhotos(results)
    } catch (err) {
      setError(err instanceof Error ? err.message : '搜索失败')
      console.error('搜索失败:', err)
    } finally {
      setLoading(false)
    }
  }, [])

  const clear = useCallback(() => {
    setPhotos([])
    setError(null)
  }, [])

  return {
    photos,
    loading,
    error,
    search,
    clear
  }
}

// 单个相册Hook
export function useSupabaseAlbum(albumId: string) {
  const [album, setAlbum] = useState<Album | null>(null)
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  useEffect(() => {
    if (!albumId) return

    const loadAlbum = async () => {
      try {
        setLoading(true)
        setError(null)
        const albumData = await AlbumService.getAlbumById(albumId)
        setAlbum(albumData)
      } catch (err) {
        setError(err instanceof Error ? err.message : '加载相册失败')
        console.error('加载相册失败:', err)
      } finally {
        setLoading(false)
      }
    }

    loadAlbum()
  }, [albumId])

  return {
    album,
    loading,
    error
  }
}

// 数据转换工具函数
export function convertSupabasePhotoToLocal(supabasePhoto: Photo) {
  return {
    id: parseInt(supabasePhoto.id),
    src: `/images/${supabasePhoto.filename}`,
    title: supabasePhoto.title || '',
    year: supabasePhoto.date_taken || '',
    description: supabasePhoto.description || '',
    optimized: true,
    fileSize: supabasePhoto.file_size || undefined,
    priority: 'normal' as const
  }
}

// 批量转换
export function convertSupabasePhotosToLocal(supabasePhotos: Photo[]) {
  return supabasePhotos.map(convertSupabasePhotoToLocal)
}
